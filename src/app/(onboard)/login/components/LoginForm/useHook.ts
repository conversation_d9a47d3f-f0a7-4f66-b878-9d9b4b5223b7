'use client';

import { useState, useCallback } from 'react';
import { FormDataI, FormErrorsI } from './types';

export function useLoginForm() {
  const [formData, setFormData] = useState<FormDataI>({
    email: '',
    password: '',
  });

  const [errors, setErrors] = useState<FormErrorsI>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));

      if (errors[name as keyof FormErrorsI]) {
        setErrors(prev => ({ ...prev, [name]: undefined }));
      }
    },
    [errors]
  );

  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrorsI = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Please enter your email or phone number';
    }

    if (!formData.password) {
      newErrors.password = 'Please enter your password';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) {
        return;
      }

      setIsLoading(true);
      setErrors({});

      try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('Sign in data:', formData);
      } catch (_err) {
        setErrors({ general: `An error occurred. Please try again. ${_err}` });
      } finally {
        setIsLoading(false);
      }
    },
    [formData, validateForm]
  );

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  return {
    formData,
    errors,
    isLoading,
    showPassword,
    handleInputChange,
    handleSubmit,
    togglePasswordVisibility,
  };
}
