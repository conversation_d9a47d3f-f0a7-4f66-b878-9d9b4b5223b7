'use client';

import Link from 'next/link';
import { LoginForm } from './components/LoginForm';
import { SocialLogin } from './components/SocialLogin';

export default function LoginPage() {
  return (
    <div className="min-h-full bg-white flex flex-col">
      <div className="flex-1 flex flex-col justify-center py-6 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-sm lg:shadow-lg lg:p-6 rounded-sm">
          <div className="text-left mb-6">
            <h1 className="text-3xl font-semibold text-black mb-6">Sign in</h1>
          </div>

          <div className="space-y-4">
            <SocialLogin />

            <div className="relative my-4">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white text-gray-500">or</span>
              </div>
            </div>
            <LoginForm />
          </div>
        </div>
        <div className="text-center mt-6">
          <p className="text-sm font-medium text-gray-600">
            New to Navicater?{' '}
            <Link href="#" className="text-primary font-semibold hover:underline">
              Join now
            </>
          </p>
        </div>
      </div>
    </div>
  );
}
