'use client';

import { useState } from 'react';
import { Button, Input } from '@/components';

interface ISignUpFormData {
  email: string;
  password: string;
  confirmPassword: string;
}

interface ISignUpFormErrors {
  email?: string;
  password?: string;
  confirmPassword?: string;
  general?: string;
}

function useSignUpForm() {
  const [formData, setFormData] = useState<ISignUpFormData>({
    email: '',
    password: '',
    confirmPassword: '',
  });

  const [errors, setErrors] = useState<ISignUpFormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const validateEmail = (email: string): string | undefined => {
    if (!email) {
      return 'Email is required';
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;

    if (!emailRegex.test(email) && !phoneRegex.test(email)) {
      return 'Please enter a valid email address or phone number';
    }

    return undefined;
  };

  const validatePassword = (password: string): string | undefined => {
    if (!password) {
      return 'Password is required';
    }

    if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    }

    return undefined;
  };

  const validateConfirmPassword = (confirmPassword: string, password: string): string | undefined => {
    if (!confirmPassword) {
      return 'Please confirm your password';
    }

    if (confirmPassword !== password) {
      return 'Passwords do not match';
    }

    return undefined;
  };

  const validateForm = (): boolean => {
    const newErrors: ISignUpFormErrors = {};

    const emailError = validateEmail(formData.email);
    if (emailError) {
      newErrors.email = emailError;
    }

    const passwordError = validatePassword(formData.password);
    if (passwordError) {
      newErrors.password = passwordError;
    }

    const confirmPasswordError = validateConfirmPassword(formData.confirmPassword, formData.password);
    if (confirmPasswordError) {
      newErrors.confirmPassword = confirmPasswordError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    if (errors[name as keyof ISignUpFormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined,
      }));
    }

    // Clear confirm password error when password changes
    if (name === 'password' && errors.confirmPassword) {
      setErrors(prev => ({
        ...prev,
        confirmPassword: undefined,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      console.log('Sign up attempt:', {
        email: formData.email,
        password: formData.password,
        rememberMe
      });

      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Sign up successful');
    } catch (error) {
      console.error('Sign up error:', error);
      setErrors({
        general: 'Sign up failed. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(prev => !prev);
  };

  return {
    formData,
    errors,
    isLoading,
    handleInputChange,
    handleSubmit,
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    rememberMe,
    setRememberMe,
  };
}

export function SignUpForm() {
  const {
    formData,
    errors,
    isLoading,
    handleInputChange,
    handleSubmit,
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    rememberMe,
    setRememberMe,
  } = useSignUpForm();

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Input
          id="email"
          name="email"
          type="text"
          autoComplete="username"
          required
          value={formData.email}
          onChange={handleInputChange}
          placeholder="Email or phone number"
          className="w-full"
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email}</p>
        )}
      </div>

      <div className="relative">
        <Input
          id="password"
          name="password"
          type={showPassword ? 'text' : 'password'}
          autoComplete="new-password"
          required
          value={formData.password}
          onChange={handleInputChange}
          placeholder="New Password"
          className="w-full pr-16"
        />
        <button
          type="button"
          className="absolute inset-y-0 right-0 pr-3 flex items-center"
          onClick={togglePasswordVisibility}
        >
          <span className="text-[#0077B5] text-sm font-medium hover:underline">
            {showPassword ? 'Hide' : 'Show'}
          </span>
        </button>
        {errors.password && (
          <p className="mt-1 text-sm text-red-600">{errors.password}</p>
        )}
      </div>

      <div className="relative">
        <Input
          id="confirmPassword"
          name="confirmPassword"
          type={showConfirmPassword ? 'text' : 'password'}
          autoComplete="new-password"
          required
          value={formData.confirmPassword}
          onChange={handleInputChange}
          placeholder="Confirm Password"
          className="w-full pr-16"
        />
        <button
          type="button"
          className="absolute inset-y-0 right-0 pr-3 flex items-center"
          onClick={toggleConfirmPasswordVisibility}
        >
          <span className="text-[#0077B5] text-sm font-medium hover:underline">
            {showConfirmPassword ? 'Hide' : 'Show'}
          </span>
        </button>
        {errors.confirmPassword && (
          <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
        )}
      </div>

      <div className="flex items-start space-x-2">
        <input
          id="remember-me"
          name="remember-me"
          type="checkbox"
          checked={rememberMe}
          onChange={(e) => setRememberMe(e.target.checked)}
          className="h-4 w-4 text-[#0077B5] focus:ring-[#0077B5] border-gray-300 rounded mt-0.5"
        />
        <label htmlFor="remember-me" className="text-sm text-gray-700">
          Remember me
        </label>
      </div>

      <div className="text-xs text-gray-600 leading-relaxed">
        By clicking Agree & Join or Continue, you agree to the LinkedIn{' '}
        <a href="#" className="text-[#0077B5] hover:underline">
          User Agreement
        </a>
        ,{' '}
        <a href="#" className="text-[#0077B5] hover:underline">
          Privacy Policy
        </a>
        , and{' '}
        <a href="#" className="text-[#0077B5] hover:underline">
          Cookie Policy
        </a>
        .
      </div>

      {errors.general && (
        <p className="text-sm text-red-600">{errors.general}</p>
      )}

      <div className="w-full">
        <Button
          className="w-full rounded-full bg-[#0077B5] hover:bg-[#005885] text-white text-base font-semibold py-3"
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? 'Creating account...' : 'Agree & Join'}
        </Button>
      </div>
    </form>
  );
}